"use client";

import { useState, useRef, useEffect } from "react";
import { createPortal } from "react-dom";
import { Calendar } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface DateRangePickerProps {
  startDate?: string;
  endDate?: string;
  onStartDateChange: (date: string) => void;
  onEndDateChange: (date: string) => void;
  placeholder?: string;
  className?: string;
  onInteractionStart?: () => void;
  onInteractionEnd?: () => void;
  onInteractionCountChange?: (count: number) => void;
}

export function DateRangePicker({
  startDate = "",
  endDate = "",
  onStartDateChange,
  onEndDateChange,
  placeholder = "Select date range",
  className = "",
  onInteractionStart,
  onInteractionEnd,
  onInteractionCountChange
}: DateRangePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [tempStartDate, setTempStartDate] = useState(startDate);
  const [tempEndDate, setTempEndDate] = useState(endDate);
  const [mounted, setMounted] = useState(false);
  const [isDateInputActive, setIsDateInputActive] = useState(false);
  const [interactionCount, setInteractionCount] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const panelRef = useRef<HTMLDivElement>(null);

  // 确保组件已挂载（避免 SSR 问题）
  useEffect(() => {
    setMounted(true);
  }, []);

  // 同步交互计数到父组件
  useEffect(() => {
    onInteractionCountChange?.(interactionCount);
  }, [interactionCount, onInteractionCountChange]);

  // 增加交互计数的辅助函数
  const incrementInteraction = () => {
    setInteractionCount(prev => prev + 1);
    onInteractionStart?.();
    setIsDateInputActive(true);
  };

  // 减少交互计数的辅助函数
  const decrementInteraction = () => {
    setInteractionCount(prev => {
      const newCount = Math.max(0, prev - 1);
      if (newCount === 0) {
        setIsDateInputActive(false);
        onInteractionEnd?.();
      }
      return newCount;
    });
  };

  // 格式化显示文本
  const getDisplayText = () => {
    if (startDate && endDate) {
      return `${startDate} - ${endDate}`;
    } else if (startDate) {
      return `${startDate} - `;
    } else if (endDate) {
      return ` - ${endDate}`;
    }
    return "";
  };

  // 点击外部关闭 - 修复版本，增强对原生日期选择器的支持
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 如果有活跃的交互或日期输入处于活动状态，不关闭面板
      if (isDateInputActive || interactionCount > 0) {
        console.log('[DateRangePicker] Not closing due to active interaction', { isDateInputActive, interactionCount });
        return;
      }

      const target = event.target as HTMLElement;

      // 检查点击是否在容器内或面板内
      const isInsideContainer = containerRef.current && containerRef.current.contains(target);
      const isInsidePanel = panelRef.current && panelRef.current.contains(target);

      // 检查是否点击了日期输入控件
      const isDateInput = target.tagName === 'INPUT' && target.getAttribute('type') === 'date';

      // 检查是否是浏览器原生日期选择器的一部分
      const isNativeDatePicker = target.closest('[role="dialog"]:not([data-radix-dialog-content])') ||
                                 target.closest('[role="listbox"]') ||
                                 target.closest('[role="grid"]') ||
                                 target.closest('[role="gridcell"]') ||
                                 target.matches('[data-testid*="date"]') ||
                                 target.matches('[class*="date"]') ||
                                 target.matches('[class*="calendar"]') ||
                                 target.matches('[class*="picker"]') ||
                                 target.matches('[aria-label*="date"]') ||
                                 target.matches('[aria-label*="calendar"]');

      // 检查是否是模态框相关元素（防止与父模态框冲突）
      const isModalElement = target.closest('[data-radix-dialog-content]') ||
                             target.closest('.fixed.inset-0') ||
                             target.matches('[data-state="open"]');

      // 只有当点击既不在容器内也不在面板内，且不是日期相关元素，也不是模态框元素时才关闭
      if (!isInsideContainer && !isInsidePanel && !isDateInput && !isNativeDatePicker && !isModalElement) {
        console.log('[DateRangePicker] Closing due to outside click');
        setIsOpen(false);
        setIsDateInputActive(false);
        setInteractionCount(0);
      }
    };

    if (isOpen && mounted) {
      // 使用 setTimeout 避免立即触发
      const timer = setTimeout(() => {
        document.addEventListener("mousedown", handleClickOutside, true); // 使用捕获阶段
      }, 100);

      return () => {
        clearTimeout(timer);
        document.removeEventListener("mousedown", handleClickOutside, true);
      };
    }
  }, [isOpen, mounted, isDateInputActive]);

  // 应用选择的日期
  const handleApply = () => {
    onStartDateChange(tempStartDate);
    onEndDateChange(tempEndDate);
    setIsOpen(false);
  };

  // 清空日期
  const handleClear = () => {
    setTempStartDate("");
    setTempEndDate("");
    onStartDateChange("");
    onEndDateChange("");
    setIsOpen(false);
  };

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* 输入框 */}
      <div className="relative">
        <Input
          value={getDisplayText()}
          onChange={(e) => {
            const value = e.target.value;
            // 简单的日期范围解析 "YYYY-MM-DD - YYYY-MM-DD"
            const parts = value.split(' - ');
            if (parts.length === 2) {
              onStartDateChange(parts[0].trim());
              onEndDateChange(parts[1].trim());
            } else if (parts.length === 1 && parts[0].includes('-') && parts[0].length >= 10) {
              onStartDateChange(parts[0].trim());
            }
          }}
          onFocus={(e) => {
            e.stopPropagation();
            incrementInteraction();
            setIsOpen(true);
          }}
          onClick={(e) => {
            e.stopPropagation();
            incrementInteraction();
            setIsOpen(true);
          }}
          placeholder={placeholder}
          className="w-full text-xs h-8 border-gray-300 focus:border-blue-500 pr-8" />
        <Calendar
          className="absolute right-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400 cursor-pointer"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();

            if (isOpen) {
              // 关闭面板
              incrementInteraction();
              setTimeout(() => {
                setIsOpen(false);
                decrementInteraction();
              }, 100);
            } else {
              // 打开面板
              incrementInteraction();
              setIsOpen(true);
            }
          }}
          onMouseDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        />
      </div>

      {/* 弹出的日期选择面板 - 使用 Portal 确保正确的层级 */}
      {isOpen && mounted && createPortal(
        <div
          ref={panelRef}
          data-date-picker-panel="true"
          className="fixed bg-white border border-gray-300 rounded-md shadow-xl z-[99999] p-4 min-w-[400px] max-w-[500px]"
          style={{
            top: containerRef.current ? containerRef.current.getBoundingClientRect().bottom + window.scrollY + 8 : 0,
            left: containerRef.current ? Math.max(10, containerRef.current.getBoundingClientRect().left + window.scrollX) : 10
          }}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
          onMouseDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
          onMouseUp={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <div className="flex gap-4">
            {/* Start date calendar */}
            <div className="flex-1">
              <label className="block text-xs font-medium text-gray-600 mb-2">
                Start Date
              </label>
              <Input
                type="date"
                value={tempStartDate}
                onChange={(e) => {
                  e.stopPropagation();
                  setTempStartDate(e.target.value);
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  console.log('[DateRangePicker] Start date input clicked');
                  incrementInteraction();
                }}
                onFocus={(e) => {
                  e.stopPropagation();
                  console.log('[DateRangePicker] Start date input focused');
                  incrementInteraction();
                }}
                onBlur={(e) => {
                  e.stopPropagation();
                  console.log('[DateRangePicker] Start date input blurred');
                  // 延迟重置状态，给原生日期选择器时间完成操作
                  setTimeout(() => {
                    decrementInteraction();
                  }, 1000);
                }}
                onMouseDown={(e) => {
                  e.stopPropagation();
                  console.log('[DateRangePicker] Start date input mousedown');
                }}
                className="w-full text-xs h-8 border-gray-300 focus:border-blue-500" />
            </div>

            {/* End date calendar */}
            <div className="flex-1">
              <label className="block text-xs font-medium text-gray-600 mb-2">
                End Date
              </label>
              <Input
                type="date"
                value={tempEndDate}
                onChange={(e) => {
                  e.stopPropagation();
                  setTempEndDate(e.target.value);
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  console.log('[DateRangePicker] End date input clicked');
                  incrementInteraction();
                }}
                onFocus={(e) => {
                  e.stopPropagation();
                  console.log('[DateRangePicker] End date input focused');
                  incrementInteraction();
                }}
                onBlur={(e) => {
                  e.stopPropagation();
                  console.log('[DateRangePicker] End date input blurred');
                  // 延迟重置状态，给原生日期选择器时间完成操作
                  setTimeout(() => {
                    decrementInteraction();
                  }, 1000);
                }}
                onMouseDown={(e) => {
                  e.stopPropagation();
                  console.log('[DateRangePicker] End date input mousedown');
                }}
                className="w-full text-xs h-8 border-gray-300 focus:border-blue-500" />
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex justify-between items-center mt-4 pt-3 border-t border-gray-200">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handleClear();
                setInteractionCount(0);
                onInteractionEnd?.();
              }}
              className="text-xs text-gray-500 hover:text-gray-700">
              Clear
            </Button>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsOpen(false);
                  setInteractionCount(0);
                  onInteractionEnd?.();
                }}
                className="text-xs">
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  handleApply();
                  setInteractionCount(0);
                  onInteractionEnd?.();
                }}
                className="text-xs bg-blue-600 hover:bg-blue-700">
                Confirm
              </Button>
            </div>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
}
